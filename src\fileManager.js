const fs = require('fs').promises;
const path = require('path');
const {
    getAllFiles,
    createDestinationPath,
    ensureDirectoryExists,
    generateUniqueFilename,
    findDuplicatesByHash,
    createLogEntry,
    formatFileSize
} = require('./utils');

class FileManager {
    constructor() {
        this.logEntries = [];
        this.stats = {
            totalFiles: 0,
            copiedFiles: 0,
            skippedFiles: 0,
            duplicatesHandled: 0,
            errors: []
        };
    }

    /**
     * Main method to copy and organize files
     */
    async copyFiles(basePath, destPath, options = {}, progressCallback = null) {
        try {
            this.logEntries = [];
            this.stats = {
                totalFiles: 0,
                copiedFiles: 0,
                skippedFiles: 0,
                duplicatesHandled: 0,
                errors: []
            };

            const {
                enableDuplicateCheck = true,
                duplicateAction = 'rename',
                createLogs = true,
                preserveStructure = true
            } = options;

            this.addLog('Starting file copy and organization process');
            this.addLog(`Source: ${basePath}`);
            this.addLog(`Destination: ${destPath}`);
            this.addLog(`Options: ${JSON.stringify(options)}`);

            // Create root folders
            await this.createRootFolders(destPath);

            // Get all files from base directory
            const allFiles = await getAllFiles(basePath);
            this.stats.totalFiles = allFiles.length;

            this.addLog(`Found ${allFiles.length} files to process`);

            if (progressCallback) {
                progressCallback({
                    percentage: 0,
                    status: `Found ${allFiles.length} files`,
                    stats: { processed: 0, total: allFiles.length }
                });
            }

            // Process each file
            for (let i = 0; i < allFiles.length; i++) {
                const filePath = allFiles[i];

                try {
                    await this.processFile(filePath, basePath, destPath, enableDuplicateCheck, duplicateAction);

                    const percentage = Math.round(((i + 1) / allFiles.length) * 100);

                    if (progressCallback) {
                        progressCallback({
                            percentage,
                            status: `Processing file ${i + 1} of ${allFiles.length}`,
                            currentFile: path.basename(filePath),
                            stats: { processed: i + 1, total: allFiles.length }
                        });
                    }
                } catch (error) {
                    this.stats.errors.push(`Error processing ${filePath}: ${error.message}`);
                    this.addLog(`Error processing ${filePath}: ${error.message}`, 'error');
                }
            }

            // Save log file if requested
            let logFile = null;
            if (createLogs) {
                logFile = await this.saveLogFile(destPath);
            }

            this.addLog('File copy and organization completed');
            this.addLog(`Total files: ${this.stats.totalFiles}`);
            this.addLog(`Copied files: ${this.stats.copiedFiles}`);
            this.addLog(`Skipped files: ${this.stats.skippedFiles}`);
            this.addLog(`Duplicates handled: ${this.stats.duplicatesHandled}`);
            this.addLog(`Errors: ${this.stats.errors.length}`);

            return {
                success: true,
                totalFiles: this.stats.totalFiles,
                copiedFiles: this.stats.copiedFiles,
                skippedFiles: this.stats.skippedFiles,
                duplicatesHandled: this.stats.duplicatesHandled,
                errors: this.stats.errors,
                logFile
            };

        } catch (error) {
            this.addLog(`Fatal error during copy process: ${error.message}`, 'error');
            return {
                success: false,
                error: error.message,
                totalFiles: this.stats.totalFiles,
                copiedFiles: this.stats.copiedFiles,
                errors: this.stats.errors
            };
        }
    }

    /**
     * Create root folders in destination
     */
    async createRootFolders(destPath) {
        const rootFolders = ['Image', 'Videos', 'Documents', 'Others'];

        for (const folder of rootFolders) {
            const folderPath = path.join(destPath, folder);
            await ensureDirectoryExists(folderPath);
            this.addLog(`Created root folder: ${folder}`);
        }
    }

    /**
     * Process individual file with enhanced duplicate detection
     */
    async processFile(filePath, basePath, destPath, enableDuplicateCheck, duplicateAction) {
        try {
            // Create destination path based on file type and date
            const destDir = await createDestinationPath(basePath, filePath, destPath);
            await ensureDirectoryExists(destDir);

            const fileName = path.basename(filePath);
            let destFilePath = path.join(destDir, fileName);

            // Enhanced duplicate handling with MD5 verification
            if (enableDuplicateCheck) {
                // Check for MD5 duplicates in entire destination directory
                const { sourceHash, duplicates } = await findDuplicatesByHash(filePath, destPath);

                if (duplicates.length > 0) {
                    this.stats.duplicatesHandled++;

                    if (duplicateAction === 'skip') {
                        // Don't copy the file
                        this.stats.skippedFiles++;
                        this.addLog(`Skipped duplicate: ${fileName} (MD5: ${sourceHash.substring(0, 8)}...) - identical file exists at ${path.relative(destPath, duplicates[0])}`);
                        return;
                    } else if (duplicateAction === 'rename') {
                        // Generate unique name for the file
                        destFilePath = await generateUniqueFilename(destFilePath);
                        this.addLog(`Renamed duplicate: ${fileName} -> ${path.basename(destFilePath)} (MD5: ${sourceHash.substring(0, 8)}...)`);
                    }
                } else {
                    // No MD5 duplicates found, but check for filename conflicts
                    try {
                        await fs.access(destFilePath);
                        // File with same name exists, but different content
                        destFilePath = await generateUniqueFilename(destFilePath);
                        this.addLog(`Renamed file with same name: ${fileName} -> ${path.basename(destFilePath)}`);
                    } catch (error) {
                        // File doesn't exist, proceed normally
                    }
                }
            } else {
                // Simple filename-based duplicate handling (legacy mode)
                try {
                    await fs.access(destFilePath);
                    destFilePath = await generateUniqueFilename(destFilePath);
                    this.stats.duplicatesHandled++;
                    this.addLog(`Renamed duplicate: ${fileName} -> ${path.basename(destFilePath)}`);
                } catch (error) {
                    // File doesn't exist, proceed normally
                }
            }

            // Copy the file
            await fs.copyFile(filePath, destFilePath);
            this.stats.copiedFiles++;

            // Get file size for logging
            const stats = await fs.stat(filePath);
            this.addLog(`Copied: ${fileName} (${formatFileSize(stats.size)}) -> ${path.relative(destPath, destFilePath)}`);

        } catch (error) {
            throw new Error(`Failed to process file ${filePath}: ${error.message}`);
        }
    }

    /**
     * Save log file to destination
     */
    async saveLogFile(destPath) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const logFileName = `file-copy-log-${timestamp}.txt`;
            const logFilePath = path.join(destPath, 'logs', logFileName);

            await ensureDirectoryExists(path.dirname(logFilePath));

            const logContent = this.logEntries.join('\n');
            await fs.writeFile(logFilePath, logContent, 'utf8');

            this.addLog(`Log file saved: ${logFilePath}`);
            return logFilePath;
        } catch (error) {
            this.addLog(`Error saving log file: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * Add log entry
     */
    addLog(message, type = 'info') {
        const logEntry = createLogEntry(message, type);
        this.logEntries.push(logEntry);
        console.log(logEntry);
    }
}

module.exports = { FileManager };
