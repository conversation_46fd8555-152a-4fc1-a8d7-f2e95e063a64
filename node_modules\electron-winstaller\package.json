{"name": "electron-winstaller", "version": "5.4.0", "description": "Module to generate Windows installers for Electron apps", "main": "lib/index.js", "types": "lib/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/electron/windows-installer"}, "files": ["lib", "resources", "script", "template.nuspectemplate", "vendor", "!vendor/7z.dll", "!vendor/7z.exe"], "scripts": {"install": "node ./script/select-7z-arch.js", "build": "tsc", "docs": "npx typedoc", "prepublish": "npm run build", "lint": "eslint --ext .ts src spec", "ava": "ava --timeout=60s", "test": "npm run lint && npm run ava", "tdd": "ava --watch"}, "dependencies": {"@electron/asar": "^3.2.1", "debug": "^4.1.1", "fs-extra": "^7.0.1", "lodash": "^4.17.21", "temp": "^0.9.0"}, "devDependencies": {"@types/fs-extra": "^5.0.5", "@types/lodash": "^4.17.0", "@types/node": "^20.6.0", "@types/temp": "^0.8.34", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "ava": "^5.1.1", "eslint": "^8.49.0", "eslint-plugin-ava": "^14.0.0", "ts-node": "^10.9.1", "typedoc": "0.25.13", "typescript": "^4.9.3"}, "optionalDependencies": {"@electron/windows-sign": "^1.1.2"}, "engines": {"node": ">=8.0.0"}, "ava": {"extensions": ["ts"], "files": ["spec/*.ts"], "require": ["ts-node/register/transpile-only"]}}