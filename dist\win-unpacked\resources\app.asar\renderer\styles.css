/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main content */
main {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Sections */
section {
    margin-bottom: 25px;
}

section h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3rem;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 5px;
}

/* Folder Selection */
.folder-selection {
    background: #f8fafc;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.folder-group {
    display: grid;
    gap: 20px;
}

.folder-item label {
    display: block;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
}

.folder-input-group {
    display: flex;
    gap: 10px;
}

.folder-input-group input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    background: white;
}

.folder-input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.folder-stats {
    margin-top: 8px;
    font-size: 12px;
    color: #718096;
    font-style: italic;
}

/* Options Section */
.options-section {
    background: #f0fff4;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #c6f6d5;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    color: #2d3748;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
}

.btn-icon {
    font-size: 18px;
}

/* Action Section */
.action-section {
    text-align: center;
    padding: 20px;
    background: #fafafa;
    border-radius: 10px;
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Progress Section */
.progress-section {
    background: #fff5f5;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #fed7d7;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-header h3 {
    margin: 0;
    color: #c53030;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #38a169);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    font-size: 14px;
    color: #4a5568;
}

/* Results Section */
.results-section {
    background: #f0fff4;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #c6f6d5;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.result-item {
    background: white;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    text-align: center;
}

.result-item.error {
    background: #fed7d7;
    border-color: #fc8181;
    color: #c53030;
}

.log-file-info {
    background: #e6fffa;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #81e6d9;
    font-size: 14px;
    color: #234e52;
    margin-top: 15px;
}

.success-notification {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Log Section */
.log-section {
    background: #1a202c;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 10px;
}

.log-section h3 {
    color: #e2e8f0;
    border-bottom-color: #4a5568;
}

.log-container {
    max-height: 200px;
    overflow-y: auto;
    background: #2d3748;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.log-entry.success {
    color: #68d391;
}

.log-entry.error {
    color: #fc8181;
}

.log-entry.warning {
    color: #f6e05e;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    main {
        padding: 20px;
    }

    .action-section {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .progress-details {
        grid-template-columns: 1fr;
    }
}
