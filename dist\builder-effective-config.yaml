directories:
  output: dist
  buildResources: build
appId: com.fileorganizer.verifier
productName: File Organizer & Verifier
files:
  - filter:
      - main.js
      - preload.js
      - renderer/**/*
      - src/**/*
      - package.json
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  forceCodeSigning: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: File Organizer & Verifier
portable:
  artifactName: FileOrganizerVerifier-${version}-portable.exe
electronVersion: 36.3.1
