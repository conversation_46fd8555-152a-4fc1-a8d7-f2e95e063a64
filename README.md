# File Organizer & Verifier

A powerful Electron desktop application for Windows that efficiently organizes files by type and creation date, with built-in MD5 hash verification for data integrity.

## Features

### 🗂️ **Smart File Organization**
- Automatically categorizes files into:
  - **Images**: JPG, PNG, GIF, BMP, TIFF, WebP, SVG, RAW formats
  - **Videos**: MP4, AVI, MKV, MOV, WMV, FLV, WebM, and more
  - **Documents**: PDF, DOC, XLS, PPT, TXT, RTF, ODT formats
  - **Others**: All other file types (preserves original structure)

### 📅 **Date-Based Organization**
- Creates Year/Month folder structure based on file creation date
- Format: `Category/Year/MonthName/`
- Example: `Images/2024/May/photo.jpg`

### ✅ **MD5 Hash Verification**
- Complete file integrity checking
- Compares source and destination file hashes
- Detailed verification reports
- Success notifications for verified files

### 🔄 **Advanced Features**
- **Duplicate Handling**: Automatic detection and renaming
- **Progress Tracking**: Real-time progress bars and status updates
- **Comprehensive Logging**: Detailed operation logs with timestamps
- **Error Handling**: Graceful error management with user feedback
- **Modern UI**: Clean, responsive interface with dark/light themes

## Installation & Usage

### Prerequisites
- Node.js (v14 or higher)
- Windows operating system

### Setup
1. Clone or download the project
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Application
```bash
npm start
```

For development mode with DevTools:
```bash
npm run dev
```

## How to Use

### 1. **Select Folders**
- Click "Browse" to select your **Base Folder** (source)
- Click "Browse" to select your **Destination Folder**
- View folder statistics (file count and size)

### 2. **Configure Options**
- ✅ **Handle Duplicates Automatically**: Rename duplicate files
- ✅ **Generate Detailed Logs**: Create operation log files
- ✅ **Preserve Structure for 'Others'**: Maintain original folder structure for uncategorized files

### 3. **Copy & Organize**
- Click **"Copy & Organize Files"** to start the process
- Monitor real-time progress and status updates
- View detailed activity logs

### 4. **Verify Files**
- After copying, click **"Verify Files (MD5)"**
- Compare source and destination file hashes
- Get success confirmation when all files match

## File Organization Structure

```
Destination Folder/
├── Image/
│   ├── 2024/
│   │   ├── 01-January/
│   │   ├── 02-February/
│   │   └── ...
│   └── 2023/
├── Videos/
│   ├── 2024/
│   └── ...
├── Documents/
│   ├── 2024/
│   └── ...
├── Others/
│   └── [Original folder structure preserved]
└── logs/
    ├── file-copy-log-[timestamp].txt
    └── verification-log-[timestamp].txt
```

## Technical Details

### Built With
- **Electron.js**: Cross-platform desktop app framework
- **Node.js**: Backend file operations and crypto
- **HTML/CSS/JavaScript**: Modern responsive UI
- **Native APIs**: File system operations and dialog boxes

### Security Features
- Context isolation enabled
- Node integration disabled
- Secure IPC communication via preload script
- No direct access to Node.js APIs from renderer

### Performance
- Asynchronous file operations
- Progress tracking for large file sets
- Memory-efficient streaming for hash calculations
- Error recovery and graceful degradation

## Supported File Types

### Images
`.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.tiff`, `.tif`, `.webp`, `.svg`, `.ico`, `.raw`, `.cr2`, `.nef`, `.arw`

### Videos
`.mp4`, `.avi`, `.mkv`, `.mov`, `.wmv`, `.flv`, `.webm`, `.m4v`, `.3gp`, `.mpg`, `.mpeg`, `.ts`, `.vob`

### Documents
`.pdf`, `.doc`, `.docx`, `.xls`, `.xlsx`, `.ppt`, `.pptx`, `.txt`, `.rtf`, `.odt`, `.ods`, `.odp`, `.csv`

## Logs and Reports

The application generates detailed logs for:
- File copy operations with timestamps
- Error tracking and resolution
- MD5 verification results
- Duplicate file handling
- Performance statistics

Log files are saved in the `logs/` folder within your destination directory.

## Troubleshooting

### Common Issues
1. **Permission Errors**: Run as administrator if accessing system folders
2. **Large Files**: Progress may appear slow for very large files (normal behavior)
3. **Network Drives**: Ensure stable connection for network-based folders
4. **Antivirus**: Some antivirus software may slow file operations

### Error Recovery
- The application handles errors gracefully
- Failed operations are logged with detailed error messages
- Partial operations can be resumed by running the process again

## License

MIT License - Feel free to use and modify for your needs.

## Support

For issues or feature requests, please check the application logs first, then contact support with the relevant log files.
