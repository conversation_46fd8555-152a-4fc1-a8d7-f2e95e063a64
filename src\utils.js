const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// File type mappings
const FILE_TYPES = {
    images: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.svg', '.ico', '.raw', '.cr2', '.nef', '.arw'],
    videos: ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.vob'],
    documents: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf', '.odt', '.ods', '.odp', '.csv']
};

/**
 * Determine file category based on extension
 */
function getFileCategory(filePath) {
    const ext = path.extname(filePath).toLowerCase();

    if (FILE_TYPES.images.includes(ext)) return 'Image';
    if (FILE_TYPES.videos.includes(ext)) return 'Videos';
    if (FILE_TYPES.documents.includes(ext)) return 'Documents';
    return 'Others';
}

/**
 * Get file creation date and format for folder structure
 */
async function getFileDateInfo(filePath) {
    try {
        const stats = await fs.stat(filePath);
        const creationDate = stats.birthtime;

        return {
            year: creationDate.getFullYear().toString(),
            month: String(creationDate.getMonth() + 1).padStart(2, '0'),
            monthName: creationDate.toLocaleString('default', { month: 'long' }),
            fullDate: creationDate
        };
    } catch (error) {
        console.error(`Error getting date info for ${filePath}:`, error);
        const now = new Date();
        return {
            year: now.getFullYear().toString(),
            month: String(now.getMonth() + 1).padStart(2, '0'),
            monthName: now.toLocaleString('default', { month: 'long' }),
            fullDate: now
        };
    }
}

/**
 * Create destination path based on file category and date
 */
async function createDestinationPath(basePath, filePath, destRoot) {
    const category = getFileCategory(filePath);
    const dateInfo = await getFileDateInfo(filePath);

    if (category === 'Others') {
        // Preserve original directory structure for 'Others'
        const relativePath = path.relative(basePath, path.dirname(filePath));
        return path.join(destRoot, 'Others', relativePath);
    } else {
        // Create year/month structure for categorized files
        return path.join(destRoot, category, dateInfo.year, dateInfo.monthName);
    }
}

/**
 * Ensure directory exists, create if it doesn't
 */
async function ensureDirectoryExists(dirPath) {
    try {
        await fs.access(dirPath);
    } catch (error) {
        await fs.mkdir(dirPath, { recursive: true });
    }
}

/**
 * Get all files recursively from a directory
 */
async function getAllFiles(dirPath, fileList = []) {
    try {
        const items = await fs.readdir(dirPath);

        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stats = await fs.stat(fullPath);

            if (stats.isDirectory()) {
                await getAllFiles(fullPath, fileList);
            } else if (stats.isFile()) {
                fileList.push(fullPath);
            }
        }
    } catch (error) {
        console.error(`Error reading directory ${dirPath}:`, error);
    }

    return fileList;
}

/**
 * Get directory statistics (file count and total size)
 */
async function getDirectoryStats(dirPath) {
    try {
        const files = await getAllFiles(dirPath);
        let totalSize = 0;

        for (const file of files) {
            try {
                const stats = await fs.stat(file);
                totalSize += stats.size;
            } catch (error) {
                console.error(`Error getting stats for ${file}:`, error);
            }
        }

        return {
            totalFiles: files.length,
            totalSize: totalSize
        };
    } catch (error) {
        console.error(`Error getting directory stats for ${dirPath}:`, error);
        return { totalFiles: 0, totalSize: 0 };
    }
}

/**
 * Generate unique filename if file already exists
 */
async function generateUniqueFilename(filePath) {
    let counter = 1;
    const dir = path.dirname(filePath);
    const ext = path.extname(filePath);
    const baseName = path.basename(filePath, ext);

    let newPath = filePath;

    while (true) {
        try {
            await fs.access(newPath);
            // File exists, generate new name
            newPath = path.join(dir, `${baseName}_${counter}${ext}`);
            counter++;
        } catch (error) {
            // File doesn't exist, we can use this name
            break;
        }
    }

    return newPath;
}

/**
 * Format file size in human readable format
 */
function formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';

    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * Calculate MD5 hash of a file
 */
async function calculateMD5(filePath) {
    return new Promise((resolve, reject) => {
        const hash = crypto.createHash('md5');
        const stream = require('fs').createReadStream(filePath);

        stream.on('data', (data) => {
            hash.update(data);
        });

        stream.on('end', () => {
            resolve(hash.digest('hex'));
        });

        stream.on('error', (error) => {
            reject(error);
        });
    });
}

/**
 * Find files with same MD5 hash in destination directory
 */
async function findDuplicatesByHash(sourceFilePath, destDir) {
    try {
        const sourceHash = await calculateMD5(sourceFilePath);
        const destFiles = await getAllFiles(destDir);
        const duplicates = [];

        for (const destFile of destFiles) {
            try {
                const destHash = await calculateMD5(destFile);
                if (sourceHash === destHash) {
                    duplicates.push(destFile);
                }
            } catch (error) {
                // Skip files that can't be read
                console.warn(`Could not calculate hash for ${destFile}: ${error.message}`);
            }
        }

        return { sourceHash, duplicates };
    } catch (error) {
        throw new Error(`Failed to calculate hash for ${sourceFilePath}: ${error.message}`);
    }
}

/**
 * Create log entry with timestamp
 */
function createLogEntry(message, type = 'info') {
    const timestamp = new Date().toISOString();
    return `[${timestamp}] [${type.toUpperCase()}] ${message}`;
}

module.exports = {
    getFileCategory,
    getFileDateInfo,
    createDestinationPath,
    ensureDirectoryExists,
    getAllFiles,
    getDirectoryStats,
    generateUniqueFilename,
    formatFileSize,
    calculateMD5,
    findDuplicatesByHash,
    createLogEntry,
    FILE_TYPES
};
