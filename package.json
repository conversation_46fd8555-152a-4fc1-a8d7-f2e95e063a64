{"name": "file-organizer-verifier", "version": "1.0.0", "description": "Electron desktop app for file organization and verification with MD5 hash checking", "main": "main.js", "homepage": ".", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win --config.forceCodeSigning=false", "build-portable": "electron-builder --win portable --config.forceCodeSigning=false", "dist": "electron-builder --publish=never --config.forceCodeSigning=false", "pack": "electron-builder --dir --config.forceCodeSigning=false", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "file-organizer", "md5", "verification"], "author": "File Organizer Team", "license": "MIT", "devDependencies": {"electron": "^36.3.1", "electron-builder": "^26.0.12"}, "build": {"appId": "com.fileorganizer.verifier", "productName": "File Organizer & Verifier", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "renderer/**/*", "src/**/*", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "forceCodeSigning": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "File Organizer & Verifier"}, "portable": {"artifactName": "FileOrganizerVerifier-${version}-portable.exe"}}}