{"version": 3, "file": "sign.js", "sourceRoot": "", "sources": ["../src/sign.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,8CAAwB;AACxB,kDAA4B;AAC5B,sDAA0B;AAI1B,IAAI,WAAmB,CAAC;AACxB,IAAI,uBAA+B,CAAC;AACpC,IAAI,qBAA6B,CAAC;AAClC,IAAI,aAAqB,CAAC;AAE1B;;;;;;;GAOG;AACH,SAAsB,cAAc,CAAC,OAA+B;;;;;;oBAClE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;wBACxB,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;qBACnF;oBAED,WAAW,GAAG,OAAO,CAAC,eAAe,IAAI,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC9E,uBAAuB,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;oBACjE,qBAAqB,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;oBACxE,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,2BAA2B,CAAC,CAAC;oBAE1C,qBAAM,oBAAoB,EAAE,EAAA;;oBAAhD,iBAAiB,GAAG,SAA4B;oBAEtD,qBAAM,aAAa,EAAE,EAAA;;oBAArB,SAAqB,CAAC;oBACtB,qBAAM,kBAAE,CAAC,MAAM,CAAC,aAAa,CAAC,EAAA;;oBAA9B,SAA8B,CAAC;oBAE/B,gCAAgC;oBAChC,qBAAM,kBAAE,CAAC,IAAI,CAAC,uBAAuB,EAAE,qBAAqB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAA;;oBADlF,gCAAgC;oBAChC,SAAkF,CAAC;oBAEnF,yDAAyD;oBACzD,qBAAM,iBAAiB,CAAC;4BACtB,IAAI,EAAE,uBAAuB;4BAC7B,WAAW,EAAE,OAAO,CAAC,WAAW;yBACjC,CAAC,EAAA;;oBAJF,yDAAyD;oBACzD,SAGE,CAAC;;;;;CACJ;AAvBD,wCAuBC;AAED;;;GAGG;AACH,SAAsB,aAAa;;;;;yBAC7B,kBAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAApC,wBAAoC;oBACtC,mCAAmC;oBACnC,qBAAM,kBAAE,CAAC,IAAI,CAAC,qBAAqB,EAAE,uBAAuB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAA;;oBADlF,mCAAmC;oBACnC,SAAkF,CAAC;oBACnF,qBAAM,kBAAE,CAAC,MAAM,CAAC,qBAAqB,CAAC,EAAA;;oBAAtC,SAAsC,CAAC;;;;;;CAE1C;AAND,sCAMC;AAED;;;;;;;;;;;;;;;GAeG;AACH,SAAe,oBAAoB;;;;;;;oBAED,sFAAa,wBAAwB,QAAC;;oBAA5D,iBAAiB,GAAK,CAAA,SAAsC,CAAA,kBAA3C;oBACzB,sBAAO,iBAAiB,EAAC;;;oBAErB,OAAO,GAAI,gGAAgG,CAAC;oBAEhH,IAAI,gBAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;wBACzC,OAAO,IAAI,2CAAoC,OAAO,CAAC,OAAO,+HAA4H,CAAC;qBAC5L;yBAAM;wBACL,OAAO,IAAI,WAAI,OAAK,CAAE,CAAC;qBACxB;oBAED,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;;;;;CAE5B"}