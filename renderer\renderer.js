// DOM Elements
const elements = {
    baseFolderPath: document.getElementById('baseFolderPath'),
    destFolderPath: document.getElementById('destFolderPath'),
    selectBaseFolder: document.getElementById('selectBaseFolder'),
    selectDestFolder: document.getElementById('selectDestFolder'),
    copyButton: document.getElementById('copyButton'),
    verifyButton: document.getElementById('verifyButton'),
    progressSection: document.getElementById('progressSection'),
    progressTitle: document.getElementById('progressTitle'),
    progressPercentage: document.getElementById('progressPercentage'),
    progressFill: document.getElementById('progressFill'),
    progressStatus: document.getElementById('progressStatus'),
    progressStats: document.getElementById('progressStats'),
    resultsSection: document.getElementById('resultsSection'),
    resultsContent: document.getElementById('resultsContent'),
    logContainer: document.getElementById('logContainer'),
    baseStats: document.getElementById('baseStats'),
    destStats: document.getElementById('destStats'),
    enableDuplicateCheck: document.getElementById('enableDuplicateCheck'),
    duplicateOptions: document.getElementById('duplicateOptions'),
    createLogs: document.getElementById('createLogs'),
    preserveStructure: document.getElementById('preserveStructure')
};

// Application state
let appState = {
    basePath: '',
    destPath: '',
    isProcessing: false,
    copyCompleted: false
};

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    initializeEventListeners();
    addLog('Application initialized. Ready to organize files.');
});

function initializeEventListeners() {
    // Folder selection
    elements.selectBaseFolder.addEventListener('click', () => selectFolder('base'));
    elements.selectDestFolder.addEventListener('click', () => selectFolder('dest'));

    // Action buttons
    elements.copyButton.addEventListener('click', startCopyProcess);
    elements.verifyButton.addEventListener('click', startVerifyProcess);

    // Duplicate check toggle
    elements.enableDuplicateCheck.addEventListener('change', toggleDuplicateOptions);

    // Progress listeners
    window.electronAPI.onCopyProgress(handleCopyProgress);
    window.electronAPI.onVerifyProgress(handleVerifyProgress);
}

function toggleDuplicateOptions() {
    const isEnabled = elements.enableDuplicateCheck.checked;
    elements.duplicateOptions.style.display = isEnabled ? 'block' : 'none';
    elements.duplicateOptions.style.opacity = isEnabled ? '1' : '0.5';
}

async function selectFolder(type) {
    try {
        const title = type === 'base' ? 'Select Base Folder (Source)' : 'Select Destination Folder';
        const folderPath = await window.electronAPI.selectFolder(title);

        if (folderPath) {
            if (type === 'base') {
                appState.basePath = folderPath;
                elements.baseFolderPath.value = folderPath;
                await updateFolderStats('base', folderPath);
                addLog(`Base folder selected: ${folderPath}`, 'success');
            } else {
                appState.destPath = folderPath;
                elements.destFolderPath.value = folderPath;
                await updateFolderStats('dest', folderPath);
                addLog(`Destination folder selected: ${folderPath}`, 'success');
            }

            updateButtonStates();
        }
    } catch (error) {
        addLog(`Error selecting folder: ${error.message}`, 'error');
    }
}

async function updateFolderStats(type, folderPath) {
    try {
        const stats = await window.electronAPI.getFolderStats(folderPath);
        const statsElement = type === 'base' ? elements.baseStats : elements.destStats;

        if (stats.totalFiles > 0) {
            const sizeInMB = (stats.totalSize / (1024 * 1024)).toFixed(2);
            statsElement.textContent = `${stats.totalFiles} files, ${sizeInMB} MB`;
        } else {
            statsElement.textContent = 'Empty folder or no accessible files';
        }
    } catch (error) {
        console.error('Error getting folder stats:', error);
    }
}

function updateButtonStates() {
    const bothFoldersSelected = appState.basePath && appState.destPath;
    elements.copyButton.disabled = !bothFoldersSelected || appState.isProcessing;
    elements.verifyButton.disabled = !bothFoldersSelected || !appState.copyCompleted || appState.isProcessing;
}

async function startCopyProcess() {
    if (appState.isProcessing) return;

    appState.isProcessing = true;
    appState.copyCompleted = false;
    updateButtonStates();

    showProgress('Copying & Organizing Files');
    addLog('Starting file copy and organization process...', 'success');

    try {
        const duplicateAction = document.querySelector('input[name="duplicateAction"]:checked')?.value || 'rename';
        const options = {
            enableDuplicateCheck: elements.enableDuplicateCheck.checked,
            duplicateAction: duplicateAction,
            createLogs: elements.createLogs.checked,
            preserveStructure: elements.preserveStructure.checked
        };

        const result = await window.electronAPI.copyFiles(appState.basePath, appState.destPath, options);

        if (result.success) {
            appState.copyCompleted = true;
            showResults(result);
            addLog(`Copy process completed successfully. ${result.totalFiles} files processed.`, 'success');
        } else {
            addLog(`Copy process failed: ${result.error}`, 'error');
        }
    } catch (error) {
        addLog(`Copy process error: ${error.message}`, 'error');
    } finally {
        appState.isProcessing = false;
        updateButtonStates();
        hideProgress();
    }
}

async function startVerifyProcess() {
    if (appState.isProcessing || !appState.copyCompleted) return;

    appState.isProcessing = true;
    updateButtonStates();

    showProgress('Verifying Files (MD5)');
    addLog('Starting MD5 hash verification process...', 'success');

    try {
        const result = await window.electronAPI.verifyFiles(appState.basePath, appState.destPath);

        if (result.success) {
            showResults(result);
            addLog(`Verification completed successfully. ${result.verifiedFiles} files verified.`, 'success');

            if (result.allFilesMatch) {
                showSuccessPopup('All files verified successfully! ✅');
            } else {
                addLog(`Warning: ${result.mismatchedFiles} files had hash mismatches.`, 'warning');
            }
        } else {
            addLog(`Verification failed: ${result.error}`, 'error');
        }
    } catch (error) {
        addLog(`Verification error: ${error.message}`, 'error');
    } finally {
        appState.isProcessing = false;
        updateButtonStates();
        hideProgress();
    }
}

function handleCopyProgress(progress) {
    updateProgress(progress);
    if (progress.currentFile) {
        addLog(`Copying: ${progress.currentFile}`);
    }
}

function handleVerifyProgress(progress) {
    updateProgress(progress);
    if (progress.currentFile) {
        addLog(`Verifying: ${progress.currentFile}`);
    }
}

function updateProgress(progress) {
    const percentage = Math.round(progress.percentage || 0);
    elements.progressPercentage.textContent = `${percentage}%`;
    elements.progressFill.style.width = `${percentage}%`;
    elements.progressStatus.textContent = progress.status || 'Processing...';

    if (progress.stats) {
        elements.progressStats.textContent =
            `${progress.stats.processed || 0} of ${progress.stats.total || 0} files`;
    }
}

function showProgress(title) {
    elements.progressTitle.textContent = title;
    elements.progressSection.style.display = 'block';
    elements.resultsSection.style.display = 'none';
    updateProgress({ percentage: 0, status: 'Initializing...' });
}

function hideProgress() {
    elements.progressSection.style.display = 'none';
}

function showResults(result) {
    elements.resultsSection.style.display = 'block';

    let html = '<div class="results-grid">';

    if (result.totalFiles !== undefined) {
        html += `<div class="result-item"><strong>Total Files:</strong> ${result.totalFiles}</div>`;
    }
    if (result.copiedFiles !== undefined) {
        html += `<div class="result-item"><strong>Copied Files:</strong> ${result.copiedFiles}</div>`;
    }
    if (result.skippedFiles !== undefined && result.skippedFiles > 0) {
        html += `<div class="result-item"><strong>Skipped Files:</strong> ${result.skippedFiles}</div>`;
    }
    if (result.verifiedFiles !== undefined) {
        html += `<div class="result-item"><strong>Verified Files:</strong> ${result.verifiedFiles}</div>`;
    }
    if (result.duplicatesHandled !== undefined) {
        html += `<div class="result-item"><strong>Duplicates Handled:</strong> ${result.duplicatesHandled}</div>`;
    }
    if (result.errors && result.errors.length > 0) {
        html += `<div class="result-item error"><strong>Errors:</strong> ${result.errors.length}</div>`;
    }

    html += '</div>';

    if (result.logFile) {
        html += `<div class="log-file-info">Detailed log saved to: ${result.logFile}</div>`;
    }

    elements.resultsContent.innerHTML = html;
}

function showSuccessPopup(message) {
    // Create a simple success notification
    const notification = document.createElement('div');
    notification.className = 'success-notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #48bb78;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1000;
        font-weight: 600;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `[${timestamp}] ${message}`;

    elements.logContainer.appendChild(logEntry);
    elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    window.electronAPI.removeAllListeners('copy-progress');
    window.electronAPI.removeAllListeners('verify-progress');
});
