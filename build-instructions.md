# Build Instructions for File Organizer & Verifier

## 🚀 Building Executable Files

The application now supports building standalone executable files for Windows distribution.

### Available Build Commands

#### 1. **Development Mode**
```bash
npm start          # Run in development mode
npm run dev        # Run with <PERSON><PERSON><PERSON><PERSON> open
```

#### 2. **Build Installer (NSIS)**
```bash
npm run build-win
```
Creates a Windows installer (.exe) with the following features:
- Custom installation directory selection
- Desktop shortcut creation
- Start menu shortcut
- Uninstaller included
- Professional installer interface

#### 3. **Build Portable Executable**
```bash
npm run build-portable
```
Creates a portable .exe file that:
- Runs without installation
- Can be placed on USB drives
- No registry modifications
- Self-contained executable

#### 4. **Build Both Formats**
```bash
npm run build
```
Creates both installer and portable versions.

#### 5. **Pack for Testing**
```bash
npm run pack
```
Creates unpacked application in `dist/` folder for testing without building final executables.

### 📁 Output Directory

All built files will be created in the `dist/` directory:

```
dist/
├── FileOrganizerVerifier-1.0.0-portable.exe    # Portable executable
├── File Organizer & Verifier Setup 1.0.0.exe   # Installer
└── win-unpacked/                                # Unpacked files (if using pack)
    ├── File Organizer & Verifier.exe
    ├── resources/
    └── ...
```

### 🔧 Build Configuration

The build configuration is defined in `package.json` under the `"build"` section:

- **App ID**: `com.fileorganizer.verifier`
- **Product Name**: `File Organizer & Verifier`
- **Target Architectures**: x64
- **Output Formats**: NSIS Installer + Portable EXE

### 📋 Build Requirements

#### Prerequisites
- Node.js (v14 or higher)
- npm (comes with Node.js)
- Windows (for Windows builds)

#### Dependencies
All build dependencies are automatically installed:
- `electron` - Runtime framework
- `electron-builder` - Build and packaging tool

### 🚀 Quick Build Guide

1. **Install dependencies** (if not already done):
   ```bash
   npm install
   ```

2. **Build portable executable**:
   ```bash
   npm run build-portable
   ```

3. **Find your executable**:
   - Location: `dist/FileOrganizerVerifier-1.0.0-portable.exe`
   - Size: ~150-200 MB (includes Electron runtime)
   - No installation required - just run it!

### 📦 Distribution Options

#### Option 1: Portable Executable
- **Best for**: USB drives, temporary use, no admin rights
- **Pros**: No installation, runs anywhere
- **Cons**: Larger file size

#### Option 2: NSIS Installer
- **Best for**: Permanent installation, multiple users
- **Pros**: Professional installation, smaller download
- **Cons**: Requires installation process

### 🔍 Testing Built Applications

1. **Test the portable version**:
   ```bash
   # Build portable
   npm run build-portable
   
   # Run the executable
   ./dist/FileOrganizerVerifier-1.0.0-portable.exe
   ```

2. **Test the installer**:
   ```bash
   # Build installer
   npm run build-win
   
   # Run the installer
   ./dist/"File Organizer & Verifier Setup 1.0.0.exe"
   ```

### 🛠️ Customization Options

#### Change App Icon
1. Replace `assets/icon.ico` with your custom icon
2. Rebuild the application

#### Modify Build Settings
Edit the `"build"` section in `package.json`:
- Change app name, version, or description
- Modify installer options
- Add/remove target platforms

#### Add Auto-Updates
Extend the build configuration to include update servers and signing certificates.

### 📊 Build Performance

- **Build Time**: 2-5 minutes (depending on system)
- **Output Size**: 
  - Portable EXE: ~150-200 MB
  - Installer: ~100-150 MB
- **Compression**: Automatic compression applied

### 🐛 Troubleshooting

#### Common Issues

1. **Build fails with permission errors**:
   - Run terminal as administrator
   - Check antivirus software interference

2. **Large file size**:
   - Normal for Electron apps (includes Chromium runtime)
   - Use installer version for smaller download

3. **Icon not showing**:
   - Ensure `assets/icon.ico` exists
   - Use proper ICO format (not PNG/JPG)

#### Build Logs
Check the console output during build for detailed information about the process.

### 🎉 Success!

After building, you'll have professional executable files ready for distribution:
- ✅ Standalone executable (no Node.js required)
- ✅ Professional installer with shortcuts
- ✅ Portable version for USB drives
- ✅ Windows-optimized builds

Your File Organizer & Verifier is now ready for distribution!
